* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    background: linear-gradient(135deg, #f5e6a3, #e8d078);
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    font-family: Arial, sans-serif;
}

.container {
    position: relative;
}

.face {
    position: relative;
    width: 300px;
    height: 400px;
}

/* Hair */
.hair {
    position: absolute;
    top: -20px;
    left: 20px;
    width: 260px;
    height: 120px;
    background: #2c1810;
    border-radius: 130px 130px 60px 60px;
    z-index: 1;
}

/* Face shape */
.face-shape {
    position: absolute;
    top: 40px;
    left: 50px;
    width: 200px;
    height: 240px;
    background: #d4a574;
    border-radius: 100px 100px 90px 90px;
    z-index: 2;
}

/* Forehead */
.forehead {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 80px;
    background: #d4a574;
    border-radius: 100px 100px 0 0;
}

/* Eyebrows */
.eyebrow {
    position: absolute;
    width: 35px;
    height: 8px;
    background: #2c1810;
    border-radius: 4px;
    top: 60px;
}

.left-eyebrow {
    left: 35px;
    transform: rotate(-5deg);
}

.right-eyebrow {
    right: 35px;
    transform: rotate(5deg);
}

/* Eyes */
.eye {
    position: absolute;
    width: 30px;
    height: 20px;
    background: white;
    border-radius: 50%;
    top: 75px;
    border: 1px solid #ccc;
}

.left-eye {
    left: 40px;
}

.right-eye {
    right: 40px;
}

.eyeball {
    position: relative;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    overflow: hidden;
}

.pupil {
    position: absolute;
    width: 12px;
    height: 12px;
    background: #1a1a1a;
    border-radius: 50%;
    top: 4px;
    left: 9px;
}

.highlight {
    position: absolute;
    width: 4px;
    height: 4px;
    background: white;
    border-radius: 50%;
    top: 6px;
    left: 11px;
}

/* Glasses */
.glasses {
    position: absolute;
    top: 70px;
    left: 25px;
    width: 150px;
    height: 40px;
    z-index: 3;
}

.lens {
    position: absolute;
    width: 45px;
    height: 35px;
    border: 2px solid #8B4513;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.1);
}

.left-lens {
    left: 0;
}

.right-lens {
    right: 0;
}

.bridge {
    position: absolute;
    top: 15px;
    left: 47px;
    width: 16px;
    height: 3px;
    background: #8B4513;
    border-radius: 2px;
}

.temple {
    position: absolute;
    width: 30px;
    height: 2px;
    background: #8B4513;
    top: 15px;
}

.left-temple {
    left: -25px;
}

.right-temple {
    right: -25px;
}

/* Nose */
.nose {
    position: absolute;
    top: 110px;
    left: 85px;
    width: 30px;
    height: 35px;
    background: #c49660;
    border-radius: 15px 15px 10px 10px;
}

.nostril {
    position: absolute;
    width: 4px;
    height: 6px;
    background: #a0804a;
    border-radius: 50%;
    bottom: 5px;
}

.left-nostril {
    left: 8px;
}

.right-nostril {
    right: 8px;
}

/* Mouth */
.mouth {
    position: absolute;
    top: 160px;
    left: 70px;
    width: 60px;
    height: 25px;
}

.upper-lip {
    width: 100%;
    height: 8px;
    background: #b8956a;
    border-radius: 30px 30px 0 0;
}

.lower-lip {
    width: 100%;
    height: 12px;
    background: #c49660;
    border-radius: 0 0 30px 30px;
    margin-top: 2px;
}

/* Facial hair */
.facial-hair {
    position: absolute;
    bottom: 20px;
    left: 60px;
    width: 80px;
    height: 40px;
}

.mustache {
    position: absolute;
    top: -25px;
    left: 10px;
    width: 40px;
    height: 8px;
    background: #2c1810;
    border-radius: 4px;
}

.chin-hair {
    position: absolute;
    bottom: 0;
    left: 20px;
    width: 40px;
    height: 15px;
    background: #2c1810;
    border-radius: 0 0 20px 20px;
}

/* Neck */
.neck {
    position: absolute;
    top: 280px;
    left: 110px;
    width: 80px;
    height: 60px;
    background: #d4a574;
    z-index: 1;
}

/* Shirt */
.shirt {
    position: absolute;
    top: 320px;
    left: 50px;
    width: 200px;
    height: 100px;
    background: #4a6fa5;
    border-radius: 0 0 20px 20px;
    z-index: 0;
}

.collar {
    position: absolute;
    top: 0;
    left: 50px;
    width: 100px;
    height: 30px;
    background: #4a6fa5;
    border-radius: 15px 15px 0 0;
    border: 2px solid #3a5a95;
}

.collar::before {
    content: '';
    position: absolute;
    top: 5px;
    left: 45px;
    width: 10px;
    height: 20px;
    background: #3a5a95;
    border-radius: 5px;
}
